@extends('layouts.app')

@section('title', 'Checkout - ShreeJi Jewelry')
@section('description', 'Complete your jewelry purchase with our secure checkout process.')

@section('content')
<!-- Checkout Progress -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="checkout-progress">
                    <div class="d-flex justify-content-center">
                        <div class="progress-step active">
                            <div class="step-circle">1</div>
                            <span class="step-label">Cart</span>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step active">
                            <div class="step-circle">2</div>
                            <span class="step-label">Checkout</span>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-circle">3</div>
                            <span class="step-label">Payment</span>
                        </div>
                        <div class="progress-line"></div>
                        <div class="progress-step">
                            <div class="step-circle">4</div>
                            <span class="step-label">Complete</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Checkout Form -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Checkout Form -->
            <div class="col-lg-7">
                <h2 class="font-playfair mb-4">Checkout Details</h2>
                
                <form id="checkoutForm">
                    @csrf
                    <!-- Contact Information -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Contact Information</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="billing_address[name]"
                                           value="{{ old('billing_address.name') }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="billing_address[email]"
                                           value="{{ old('billing_address.email') }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone" name="billing_address[phone]"
                                           value="{{ old('billing_address.phone') }}" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shipping Address -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Shipping Address</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="address" class="form-label">Street Address *</label>
                                    <input type="text" class="form-control" id="address" name="shipping_address[address]" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="city" class="form-label">City *</label>
                                    <input type="text" class="form-control" id="city" name="shipping_address[city]" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="state" class="form-label">State *</label>
                                    <select class="form-select" id="state" name="shipping_address[state]" required>
                                        <option value="">Select State</option>
                                        <option value="maharashtra">Maharashtra</option>
                                        <option value="delhi">Delhi</option>
                                        <option value="karnataka">Karnataka</option>
                                        <option value="gujarat">Gujarat</option>
                                        <option value="rajasthan">Rajasthan</option>
                                        <option value="tamil-nadu">Tamil Nadu</option>
                                        <option value="west-bengal">West Bengal</option>
                                        <option value="uttar-pradesh">Uttar Pradesh</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="pincode" class="form-label">PIN Code *</label>
                                    <input type="text" class="form-control" id="pincode" name="shipping_address[pincode]" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="country" class="form-label">Country *</label>
                                    <select class="form-select" id="country" name="shipping_address[country]" required>
                                        <option value="india">India</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="sameAsBilling" checked>
                                <label class="form-check-label" for="sameAsBilling">
                                    Billing address is the same as shipping address
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Billing Address -->
                    <div class="card border-0 shadow-sm mb-4" id="billingAddressCard" style="display: none;">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Billing Address</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="billingAddress" class="form-label">Street Address *</label>
                                    <input type="text" class="form-control" id="billingAddress" name="billing_address[address]">
                                </div>
                                <div class="col-md-6">
                                    <label for="billingCity" class="form-label">City *</label>
                                    <input type="text" class="form-control" id="billingCity" name="billing_address[city]">
                                </div>
                                <div class="col-md-6">
                                    <label for="billingState" class="form-label">State *</label>
                                    <select class="form-select" id="billingState" name="billing_address[state]">
                                        <option value="">Select State</option>
                                        <option value="maharashtra">Maharashtra</option>
                                        <option value="delhi">Delhi</option>
                                        <option value="karnataka">Karnataka</option>
                                        <option value="gujarat">Gujarat</option>
                                        <option value="rajasthan">Rajasthan</option>
                                        <option value="tamil-nadu">Tamil Nadu</option>
                                        <option value="west-bengal">West Bengal</option>
                                        <option value="uttar-pradesh">Uttar Pradesh</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="billingPincode" class="form-label">PIN Code *</label>
                                    <input type="text" class="form-control" id="billingPincode" name="billing_address[pincode]">
                                </div>
                                <div class="col-md-6">
                                    <label for="billingCountry" class="form-label">Country *</label>
                                    <select class="form-select" id="billingCountry" name="billing_address[country]">
                                        <option value="india">India</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shipping Method -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Shipping Method</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="shipping" id="standardShipping" value="standard" checked>
                                <label class="form-check-label" for="standardShipping">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>Standard Shipping</strong>
                                            <br><small class="text-muted">5-7 business days</small>
                                        </div>
                                        <span class="text-success fw-bold">Free</span>
                                    </div>
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="shipping" id="expressShipping" value="express">
                                <label class="form-check-label" for="expressShipping">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>Express Shipping</strong>
                                            <br><small class="text-muted">2-3 business days</small>
                                        </div>
                                        <span class="fw-bold">₹500</span>
                                    </div>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="shipping" id="overnightShipping" value="overnight">
                                <label class="form-check-label" for="overnightShipping">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <strong>Overnight Shipping</strong>
                                            <br><small class="text-muted">Next business day</small>
                                        </div>
                                        <span class="fw-bold">₹1,500</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="card border-0 shadow-sm mb-4 payment-method-card selected">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Method</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="razorpay" value="razorpay" checked>
                                <label class="form-check-label" for="razorpay">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Secure Online Payment</strong>
                                            <br><small class="text-muted">Credit/Debit Card, UPI, Net Banking, Wallets</small>
                                            <br><small class="text-success"><i class="fas fa-shield-alt me-1"></i>256-bit SSL Encrypted & Safe</small>
                                        </div>
                                        <div class="text-end">
                                            <img src="https://razorpay.com/assets/razorpay-glyph.svg" alt="Razorpay" style="height: 24px;" onerror="this.style.display='none'">
                                            <div class="mt-1">
                                                <small class="text-muted">Powered by Razorpay</small>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <!-- Payment Methods Icons -->
                            <div class="payment-methods-grid">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <i class="fab fa-cc-visa text-primary payment-method-icon" style="font-size: 24px;"></i>
                                        <br><small class="text-muted">Visa</small>
                                    </div>
                                    <div class="col-3">
                                        <i class="fab fa-cc-mastercard text-warning payment-method-icon" style="font-size: 24px;"></i>
                                        <br><small class="text-muted">Mastercard</small>
                                    </div>
                                    <div class="col-3">
                                        <i class="fas fa-mobile-alt text-success payment-method-icon" style="font-size: 24px;"></i>
                                        <br><small class="text-muted">UPI</small>
                                    </div>
                                    <div class="col-3">
                                        <i class="fas fa-university text-info payment-method-icon" style="font-size: 24px;"></i>
                                        <br><small class="text-muted">Net Banking</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Notice -->
                            <div class="mt-3 p-2 bg-success bg-opacity-10 rounded border border-success border-opacity-25">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-lock text-success me-2"></i>
                                    <small class="text-success">Your payment information is secure and encrypted</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Special Instructions -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Special Instructions</h5>
                        </div>
                        <div class="card-body p-4">
                            <textarea class="form-control" name="notes" rows="3" placeholder="Any special delivery instructions or gift message..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-5">
                <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                    <div class="card-header bg-gradient-pink text-white">
                        <h5 class="font-playfair mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body p-4">
                        <!-- Order Items -->
                        <div class="order-items mb-4">
                            @forelse($cartItems as $item)
                            <div class="d-flex align-items-center mb-3">
                                @if($item->product && $item->product->images && count($item->product->images) > 0)
                                    <img src="{{ $item->product->images[0] }}"
                                         alt="{{ $item->product->name }}" class="rounded me-3" width="60" height="60">
                                @else
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-gem text-muted"></i>
                                    </div>
                                @endif
                                <div class="flex-fill">
                                    <h6 class="mb-0">{{ $item->product->name }}</h6>
                                    <small class="text-muted">
                                        @if($item->size) Size: {{ $item->size }} | @endif
                                        Qty: {{ $item->quantity }}
                                    </small>
                                </div>
                                <span class="fw-bold">₹{{ number_format($item->total_price, 0) }}</span>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                                <p class="text-muted">Your cart is empty</p>
                                <a href="{{ route('products') }}" class="btn" style="background-color: var(--primary-brown); color: var(--primary-cream);">Continue Shopping</a>
                            </div>
                            @endforelse
                        </div>
                        
                        <hr>
                        
                        <!-- Price Breakdown -->
                        <div class="price-breakdown">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal ({{ $cartSummary['item_count'] }} items)</span>
                                <span>₹{{ number_format($cartSummary['subtotal'], 0) }}</span>
                            </div>
                            @if($cartSummary['discount'] > 0)
                            <div class="d-flex justify-content-between mb-2">
                                <span>Discount</span>
                                <span class="text-success">-₹{{ number_format($cartSummary['discount'], 0) }}</span>
                            </div>
                            @endif
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping</span>
                                <span class="{{ $cartSummary['shipping'] == 0 ? 'text-success' : '' }}" id="shippingCost">
                                    @if($cartSummary['shipping'] == 0)
                                        Free
                                    @else
                                        ₹{{ number_format($cartSummary['shipping'], 0) }}
                                    @endif
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax (GST 3%)</span>
                                <span>₹{{ number_format($cartSummary['tax'], 0) }}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total</strong>
                                <strong class="h5" style="color: var(--primary-brown);" id="totalAmount">₹{{ number_format($cartSummary['total'], 0) }}</strong>
                            </div>
                        </div>
                        
                        <!-- Proceed to Payment -->
                        <button type="button" class="btn w-100 btn-lg secure-payment-btn" onclick="proceedToPayment()">
                            <i class="fas fa-shield-alt me-2"></i>Pay Securely Now
                        </button>

                        <div class="text-center payment-security-badge">
                            <small class="text-success">
                                <i class="fas fa-lock me-1"></i>
                                Your payment is secured with 256-bit SSL encryption
                            </small>
                        </div>

                        @if(config('app.env') === 'local')
                        <div class="mt-2">
                            <small class="text-muted">Development Mode: Checkout process enabled</small>
                        </div>
                        @endif
                        
                        <!-- Security Info -->
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lock me-1"></i>
                                Your payment information is secure and encrypted
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    .checkout-progress {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }
    
    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }
    
    .progress-step.active .step-circle {
        background-color: var(--primary-brown);
        color: var(--primary-cream);
    }

    .step-label {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .progress-step.active .step-label {
        color: var(--primary-brown);
        font-weight: 600;
    }
    
    .progress-line {
        width: 80px;
        height: 2px;
        background-color: #e9ecef;
        margin: 0 10px;
        align-self: flex-start;
        margin-top: 20px;
    }
    
    .form-check-input:checked {
        background-color: var(--primary-brown);
        border-color: var(--primary-brown);
    }
    
    .form-check-label {
        width: 100%;
        cursor: pointer;
    }
    
    @media (max-width: 768px) {
        .checkout-progress {
            transform: scale(0.8);
        }
        
        .progress-line {
            width: 40px;
        }
    }

    .payment-method-card {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .payment-method-card:hover {
        border-color: #28a745;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
    }

    .payment-method-card.selected {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.05);
    }

    .secure-payment-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .secure-payment-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .secure-payment-btn:active {
        transform: translateY(0);
    }

    .payment-security-badge {
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.3);
        border-radius: 8px;
        padding: 8px 12px;
        margin-top: 10px;
    }

    .payment-methods-grid {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }

    .payment-method-icon {
        transition: transform 0.2s ease;
    }

    .payment-method-icon:hover {
        transform: scale(1.1);
    }
</style>
@endpush

@push('scripts')
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    // Toggle billing address visibility
    document.getElementById('sameAsBilling').addEventListener('change', function() {
        const billingCard = document.getElementById('billingAddressCard');
        const billingFields = ['billingAddress', 'billingCity', 'billingState', 'billingPincode'];

        if (this.checked) {
            billingCard.style.display = 'none';
            // Remove required attribute from billing fields when hidden
            billingFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.removeAttribute('required');
                    field.setCustomValidity('');
                }
            });
        } else {
            billingCard.style.display = 'block';
            // Add required attribute back to billing fields when shown
            billingFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.setAttribute('required', 'required');
                }
            });
        }
    });

    // Initialize billing address state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const sameAsBillingCheckbox = document.getElementById('sameAsBilling');
        const billingCard = document.getElementById('billingAddressCard');
        const billingFields = ['billingAddress', 'billingCity', 'billingState', 'billingPincode'];

        // If checkbox is checked by default, hide billing fields and remove required
        if (sameAsBillingCheckbox.checked) {
            billingCard.style.display = 'none';
            billingFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.removeAttribute('required');
                    field.setCustomValidity('');
                }
            });
        }
    });

    // Update shipping cost when shipping method changes
    document.querySelectorAll('input[name="shipping"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const shippingCostElement = document.getElementById('shippingCost');
            const totalAmountElement = document.getElementById('totalAmount');
            
            let shippingCost = 0;
            let shippingText = 'Free';
            
            if (this.value === 'express') {
                shippingCost = 500;
                shippingText = '₹500';
            } else if (this.value === 'overnight') {
                shippingCost = 1500;
                shippingText = '₹1,500';
            }
            
            shippingCostElement.textContent = shippingText;
            
            // Update total (base total includes tax)
            const baseTotal = {{ $cartSummary['total'] }};
            const newTotal = baseTotal + shippingCost;
            totalAmountElement.textContent = `₹${newTotal.toLocaleString()}`;
        });
    });
    
    function proceedToPayment() {
        console.log('Proceed to payment clicked');
        const form = document.getElementById('checkoutForm');

        // Basic form validation
        if (!form.checkValidity()) {
            console.log('Form validation failed');
            form.reportValidity();
            return;
        }

        // Billing address validation is now handled by required attribute toggle
        console.log('Form validation passed, submitting order');
        // Submit the form to create the order
        submitOrder();
    }

    function submitOrder() {
        console.log('Submit order called');
        const form = document.getElementById('checkoutForm');
        const formData = new FormData(form);

        // Add shipping method
        const shippingMethod = document.querySelector('input[name="shipping"]:checked');
        if (shippingMethod) {
            formData.append('shipping_method', shippingMethod.value);
            console.log('Shipping method:', shippingMethod.value);
        } else {
            console.error('No shipping method selected');
            showAlert('Please select a shipping method', 'danger');
            return;
        }

        // Set payment method to Razorpay (only option available)
        const paymentMethod = 'razorpay';
        formData.append('payment_method', paymentMethod);
        console.log('Payment method:', paymentMethod);

        // Handle billing address if same as shipping
        const sameAsBilling = document.getElementById('sameAsBilling').checked;
        if (sameAsBilling) {
            // Copy shipping address to billing address
            formData.append('billing_address[address]', formData.get('shipping_address[address]'));
            formData.append('billing_address[city]', formData.get('shipping_address[city]'));
            formData.append('billing_address[state]', formData.get('shipping_address[state]'));
            formData.append('billing_address[pincode]', formData.get('shipping_address[pincode]'));
            formData.append('billing_address[country]', formData.get('shipping_address[country]'));
            console.log('Billing address copied from shipping');
        }

        // Debug: Log form data
        console.log('Form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }

        // Show loading state
        const button = document.querySelector('button[onclick="proceedToPayment()"]');
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Secure Order...';

        // Always use guest checkout for seamless experience
        const checkoutUrl = @json(route('checkout.store'));
        console.log('Checkout URL:', checkoutUrl);

        fetch(checkoutUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Order creation response:', data);
            if (data.success) {
                if (data.payment_method === 'razorpay' && data.requires_payment) {
                    // Initialize Razorpay payment
                    console.log('Initializing Razorpay payment for order:', data.order_id);
                    showAlert('Order created! Opening secure payment gateway...', 'info');
                    initiateRazorpayPayment(data.order_id);
                } else {
                    // This shouldn't happen with current setup
                    console.log('Unexpected payment flow:', data);
                    showAlert('Payment method error. Please try again.', 'danger');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            } else {
                console.error('Order creation failed:', data);
                showAlert(data.message || 'Order failed. Please try again.', 'danger');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Order creation error:', error);
            let errorMessage = 'Network error occurred. Please check your connection and try again.';

            // Check if it's a network error or server error
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = 'Unable to connect to server. Please check your internet connection and try again.';
            } else if (error.message) {
                errorMessage = 'Order creation failed: ' + error.message;
            }

            showAlert(errorMessage, 'danger');
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    function initiateRazorpayPayment(orderId) {
        // Create Razorpay order
        fetch('{{ route("payment.create-order") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Payment initialization response:', data);
            if (data.success) {
                const options = {
                    key: data.key_id,
                    amount: data.amount,
                    currency: data.currency,
                    name: '{{ config("app.name") }}',
                    description: 'Secure Payment for Order #' + data.order_number,
                    order_id: data.order_id,
                    prefill: {
                        name: data.customer.name,
                        email: data.customer.email,
                        contact: data.customer.contact
                    },
                    theme: {
                        color: '#28a745'
                    },
                    method: {
                        netbanking: true,
                        card: true,
                        upi: true,
                        wallet: true
                    },
                    handler: function(response) {
                        console.log('Payment successful:', response);
                        showAlert('Payment successful! Verifying...', 'success');
                        verifyPayment(response, orderId);
                    },
                    modal: {
                        ondismiss: function() {
                            console.log('Payment modal dismissed');
                            showAlert('Payment was cancelled. You can try again.', 'warning');

                            // Reset button state
                            const button = document.querySelector('button[onclick="proceedToPayment()"]');
                            button.disabled = false;
                            button.innerHTML = '<i class="fas fa-shield-alt me-2"></i>Pay Securely Now';

                            // Mark payment as failed
                            handlePaymentFailure(orderId);
                        }
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } else {
                console.error('Payment initialization failed:', data);
                let errorMessage = data.message || 'Failed to initialize payment';

                // Provide specific error messages for common issues
                if (errorMessage.includes('authentication failed') || errorMessage.includes('Authentication failed')) {
                    errorMessage = 'Payment gateway is currently unavailable. Please try again later or contact support.';
                } else if (errorMessage.includes('Invalid request parameters')) {
                    errorMessage = 'Payment configuration error. Please contact support.';
                }

                showAlert(errorMessage, 'danger');

                // Reset button state
                const button = document.querySelector('button[onclick="proceedToPayment()"]');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-shield-alt me-2"></i>Pay Securely Now';
            }
        })
        .catch(error => {
            console.error('Payment initialization error:', error);
            let errorMessage = 'Payment initialization failed. Please try again.';

            // Provide more specific error messages
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = 'Unable to connect to payment gateway. Please check your internet connection.';
            } else if (error.message && error.message.includes('authentication')) {
                errorMessage = 'Payment gateway is temporarily unavailable. Please try again later.';
            }

            showAlert(errorMessage, 'danger');
            // Reset button state
            const button = document.querySelector('button[onclick="proceedToPayment()"]');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-shield-alt me-2"></i>Pay Securely Now';
        });
    }

    function verifyPayment(response, orderId) {
        fetch('{{ route("payment.verify") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Payment verified successfully! Redirecting to confirmation...', 'success');
                // Show success animation or loading
                const button = document.querySelector('button[onclick="proceedToPayment()"]');
                button.innerHTML = '<i class="fas fa-check-circle me-2"></i>Payment Successful!';
                button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1500);
            } else {
                showAlert(data.message || 'Payment verification failed. Please contact support if amount was deducted.', 'danger');
                handlePaymentFailure(orderId);
            }
        })
        .catch(error => {
            console.error('Payment verification error:', error);
            let errorMessage = 'Payment verification failed. If amount was deducted, please contact support with your order details.';

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = 'Unable to verify payment due to connection issues. Please contact support if amount was deducted.';
            }

            showAlert(errorMessage, 'danger');
            handlePaymentFailure(orderId);
        });
    }

    function handlePaymentFailure(orderId) {
        fetch('{{ route("payment.failed") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state
            const button = document.querySelector('button[onclick="proceedToPayment()"]');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-shield-alt me-2"></i>Pay Securely Now';
        })
        .catch(error => {
            console.error('Payment failure handling error:', error);
        });
    }

    function showAlert(message, type) {
        // Remove any existing alerts first
        document.querySelectorAll('.custom-checkout-alert').forEach(alert => alert.remove());

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed custom-checkout-alert`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';

        const icon = type === 'success' ? 'fas fa-check-circle text-success' :
                     type === 'danger' ? 'fas fa-exclamation-triangle text-danger' :
                     'fas fa-info-circle text-info';
        const title = type === 'success' ? 'Success!' :
                      type === 'danger' ? 'Error!' :
                      'Information';

        alertDiv.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="${icon} me-3 fs-5 mt-1"></i>
                <div class="flex-grow-1">
                    <strong class="d-block mb-1">${title}</strong>
                    <div style="font-size: 0.9em;">${message}</div>
                </div>
                <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 8 seconds for errors, 5 seconds for success
        const timeout = type === 'danger' ? 8000 : 5000;
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.classList.remove('show');
                setTimeout(() => alertDiv.remove(), 150);
            }
        }, timeout);
    }
</script>
@endpush
