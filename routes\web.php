<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', function () {
    return view('welcome');
})->name('home');
Route::get('/foo', function () {
    Artisan::call('storage:link');
    return 'Storage link created successfully! Images should now be visible.';
});


// Collections & Products
Route::get('/products', [App\Http\Controllers\ProductController::class, 'index'])->name('products');
Route::get('/collections', [App\Http\Controllers\ProductController::class, 'index'])->name('collections');
Route::get('/collections/{category}', [App\Http\Controllers\ProductController::class, 'category'])->name('collections.category');
Route::get('/product/{slug}', [App\Http\Controllers\ProductController::class, 'show'])->name('product.detail');
Route::get('/search', [App\Http\Controllers\ProductController::class, 'search'])->name('search');
Route::get('/api/products/featured', [App\Http\Controllers\ProductController::class, 'featured'])->name('api.products.featured');

// Shopping Cart & Checkout
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart');
Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
Route::post('/cart/update', [App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
Route::post('/cart/remove', [App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');
Route::post('/cart/move-to-wishlist', [App\Http\Controllers\CartController::class, 'moveToWishlist'])->name('cart.move-to-wishlist');
Route::delete('/cart', [App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');
Route::get('/api/cart/count', [App\Http\Controllers\CartController::class, 'count'])->name('api.cart.count');

// Checkout routes (guest-only experience)
Route::get('/checkout', [App\Http\Controllers\OrderController::class, 'checkout'])->name('checkout');
Route::post('/checkout', [App\Http\Controllers\OrderController::class, 'store'])->name('checkout.store');

// Public checkout success page
Route::get('/checkout/success/{order}', [App\Http\Controllers\OrderController::class, 'success'])->name('checkout.success');

// User Orders (public routes for guest and user orders)
Route::get('/orders', [App\Http\Controllers\UserController::class, 'orders'])->name('orders');
Route::get('/order/{id}', [App\Http\Controllers\UserController::class, 'orderDetail'])->name('order.detail');

// Payment Routes (Webhook doesn't need auth)
Route::post('/payment/webhook', [App\Http\Controllers\PaymentController::class, 'webhook'])->name('payment.webhook');
Route::get('/payment/test-details', [App\Http\Controllers\PaymentController::class, 'getTestDetails'])->name('payment.test-details');

// Temporary routes for testing (remove auth requirement)
Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order.temp');
Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify.temp');
Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed.temp');

// Debug route for testing Razorpay service
Route::get('/test-razorpay', function() {
    try {
        return response()->json([
            'status' => 'config_check',
            'razorpay_config' => [
                'key_id' => config('services.razorpay.key_id'),
                'key_secret' => config('services.razorpay.key_secret') ? 'SET' : 'NOT SET',
                'webhook_secret' => config('services.razorpay.webhook_secret') ? 'SET' : 'NOT SET',
            ],
            'message' => 'Razorpay configuration loaded. To test order creation, you need valid test credentials.'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
})->name('test.razorpay');

// Simple payment test route
Route::get('/test-payment-flow', function() {
    try {
        // Create a test user if not exists
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'phone' => '9999999999'
            ]
        );

        // Create a test order
        $order = \App\Models\Order::create([
            'order_number' => 'TEST_' . time(),
            'user_id' => $user->id,
            'status' => 'pending',
            'subtotal' => 100.00,
            'tax_amount' => 12.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 112.00,
            'currency' => 'INR',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'billing_address' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '9999999999',
                'address' => 'Test Address',
                'city' => 'Mumbai',
                'state' => 'maharashtra',
                'pincode' => '400001',
                'country' => 'india'
            ],
            'shipping_address' => [
                'name' => 'Test User',
                'address' => 'Test Address',
                'city' => 'Mumbai',
                'state' => 'maharashtra',
                'pincode' => '400001',
                'country' => 'india'
            ]
        ]);

        // Test Razorpay order creation
        $razorpayService = new \App\Services\RazorpayService();
        $result = $razorpayService->createOrder($order);

        return response()->json([
            'status' => 'success',
            'order_id' => $order->id,
            'razorpay_result' => $result,
            'test_url' => route('test.payment.page', $order->id)
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
})->name('test.payment.flow');

// Test payment page
Route::get('/test-payment/{order}', function($orderId) {
    $order = \App\Models\Order::findOrFail($orderId);
    return view('test-payment', compact('order'));
})->name('test.payment.page');


// Admin Authentication (Essential for store management)
Route::prefix('admin')->group(function () {
    Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLogin'])->name('admin.login');
    Route::post('/login', [App\Http\Controllers\AuthController::class, 'login'])->name('admin.login.post');
    Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout'])->name('admin.logout');
});

// Admin Routes (for store management)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Product Management
    Route::get('/products', [App\Http\Controllers\Admin\AdminController::class, 'products'])->name('products.index');
    Route::get('/products/create', [App\Http\Controllers\Admin\AdminController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [App\Http\Controllers\Admin\AdminController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateProduct'])->name('products.update');
    Route::delete('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteProduct'])->name('products.delete');

    // Category Management
    Route::get('/categories', [App\Http\Controllers\Admin\AdminController::class, 'categories'])->name('categories.index');
    Route::get('/categories/create', [App\Http\Controllers\Admin\AdminController::class, 'createCategory'])->name('categories.create');
    Route::post('/categories', [App\Http\Controllers\Admin\AdminController::class, 'storeCategory'])->name('categories.store');
    Route::get('/categories/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editCategory'])->name('categories.edit');
    Route::put('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateCategory'])->name('categories.update');
    Route::delete('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteCategory'])->name('categories.delete');

    // Order Management
    Route::get('/orders', function () {
        return view('admin.orders.index');
    })->name('orders.index');

    // Order Tracking Routes
    Route::prefix('orders/tracking')->name('orders.tracking.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\OrderTrackingController::class, 'index'])->name('index');
        Route::get('/{id}', [App\Http\Controllers\Admin\OrderTrackingController::class, 'show'])->name('show');
        Route::get('/{id}/info', [App\Http\Controllers\Admin\OrderTrackingController::class, 'getTrackingInfo'])->name('info');
        Route::post('/{id}/status', [App\Http\Controllers\Admin\OrderTrackingController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/tracking', [App\Http\Controllers\Admin\OrderTrackingController::class, 'addTracking'])->name('add-tracking');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\OrderTrackingController::class, 'bulkUpdateStatus'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\OrderTrackingController::class, 'exportOrders'])->name('export');
    });

    // Page Management
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class);
    Route::post('/pages/bulk-action', [App\Http\Controllers\Admin\PageController::class, 'bulkAction'])->name('pages.bulk-action');

    // Customer Management (for guest orders)
    Route::get('/customers', function () {
        return view('admin.customers.index');
    })->name('customers.index');
});

// Payment Routes (now public for guest checkout)
Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order');
Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify');
Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed');

// Static Pages - Keep existing routes for backward compatibility
Route::get('/about', function () {
    return view('pages.about');
})->name('about');

Route::get('/contact', function () {
    return view('pages.contact');
})->name('contact');

// Customer Service Pages - Keep existing routes for backward compatibility
Route::get('/shipping', function () {
    return view('pages.shipping');
})->name('shipping');

Route::get('/returns', function () {
    return view('pages.returns');
})->name('returns');

Route::get('/size-guide', function () {
    return view('pages.size-guide');
})->name('size-guide');

Route::get('/jewelry-care', function () {
    return view('pages.jewelry-care');
})->name('jewelry-care');

Route::get('/warranty', function () {
    return view('pages.warranty');
})->name('warranty');

// Legal Pages - Keep existing routes for backward compatibility
Route::get('/privacy-policy', function () {
    return view('pages.privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('pages.terms-of-service');
})->name('terms-of-service');

Route::get('/cookie-policy', function () {
    return view('pages.cookie-policy');
})->name('cookie-policy');

// Order Tracking (Public)
Route::get('/track-order', [App\Http\Controllers\OrderTrackingController::class, 'index'])->name('order-tracking.index');
Route::post('/track-order', [App\Http\Controllers\OrderTrackingController::class, 'track'])->name('order-tracking.track');

// Dynamic Pages System
Route::get('/page/{slug}', [App\Http\Controllers\PageController::class, 'show'])->name('page.show');
Route::get('/pages/search', [App\Http\Controllers\PageController::class, 'search'])->name('pages.search');

// Admin Routes (for product management)
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Product Management
    Route::get('/products', [App\Http\Controllers\Admin\AdminController::class, 'products'])->name('products.index');
    Route::get('/products/create', [App\Http\Controllers\Admin\AdminController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [App\Http\Controllers\Admin\AdminController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateProduct'])->name('products.update');
    Route::delete('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteProduct'])->name('products.delete');
    Route::post('/products/bulk-delete', [App\Http\Controllers\Admin\AdminController::class, 'bulkDeleteProducts'])->name('products.bulk-delete');


    // Category Management
    Route::get('/categories', [App\Http\Controllers\Admin\AdminController::class, 'categories'])->name('categories.index');
    Route::get('/categories/create', [App\Http\Controllers\Admin\AdminController::class, 'createCategory'])->name('categories.create');
    Route::post('/categories', [App\Http\Controllers\Admin\AdminController::class, 'storeCategory'])->name('categories.store');
    Route::get('/categories/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editCategory'])->name('categories.edit');
    Route::put('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateCategory'])->name('categories.update');
    Route::delete('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteCategory'])->name('categories.delete');

    // Order Management
    Route::get('/orders', function () {
        return view('admin.orders.index');
    })->name('orders.index');

    // Order Tracking Routes
    Route::prefix('orders/tracking')->name('orders.tracking.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\OrderTrackingController::class, 'index'])->name('index');
        Route::get('/{id}', [App\Http\Controllers\Admin\OrderTrackingController::class, 'show'])->name('show');
        Route::get('/{id}/info', [App\Http\Controllers\Admin\OrderTrackingController::class, 'getTrackingInfo'])->name('info');
        Route::post('/{id}/status', [App\Http\Controllers\Admin\OrderTrackingController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/tracking', [App\Http\Controllers\Admin\OrderTrackingController::class, 'addTracking'])->name('add-tracking');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\OrderTrackingController::class, 'bulkUpdateStatus'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\OrderTrackingController::class, 'exportOrders'])->name('export');
    });

    // Page Management
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class);
    Route::post('/pages/bulk-action', [App\Http\Controllers\Admin\PageController::class, 'bulkAction'])->name('pages.bulk-action');

    // Customer Management
    Route::get('/customers', function () {
        return view('admin.customers.index');
    })->name('customers.index');

    // Admin Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
        Route::put('/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('password');
        Route::put('/preferences', [App\Http\Controllers\Admin\ProfileController::class, 'updatePreferences'])->name('preferences');
        Route::get('/activity', [App\Http\Controllers\Admin\ProfileController::class, 'activity'])->name('activity');
    });
});
